import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/large_button.dart';
import 'package:cal/features/onboarding/presentation/widgets/nutritions_textfield.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_metric_card.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../features/settings/presentation/widgets/edit_ingredient_card.dart';

class ManualNutritionEditScreen extends StatefulWidget {
  final String nutritionValue;
  final CardType cardType;
  final bool isEditName;
  final Function(String) onSave;

  const ManualNutritionEditScreen({
    super.key,
    required this.nutritionValue,
    required this.cardType,
    this.isEditName = false, required this.onSave,
  });

  @override
  State<ManualNutritionEditScreen> createState() => _ManualNutritionEditScreenState();
}

class _ManualNutritionEditScreenState extends State<ManualNutritionEditScreen> {
  final TextEditingController _nutritionsController = TextEditingController();
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    _nutritionsController.text = widget.nutritionValue;
    super.initState();
  }

  @override
  void dispose() {
    _nutritionsController.dispose();
    super.dispose();
  }

  String getTitle() {
    switch (widget.cardType) {
      case CardType.cals:
        return LocaleKeys.onboarding_target_calories.tr();
      case CardType.carbs:
        return LocaleKeys.onboarding_target_carbs.tr();
      case CardType.fat:
        return LocaleKeys.onboarding_target_fats.tr();
      case CardType.protien:
        return LocaleKeys.onboarding_target_proteins.tr();
    }
  }

  String getIcon(CardType cardType) {
    switch (cardType) {
      case CardType.protien:
        return Assets.imagesProtien;
      case CardType.carbs:
        return Assets.imagesCarbs;
      case CardType.cals:
      case CardType.fat:
        return Assets.imagesFats;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            AppImage.asset(
              Assets.imagesCals,
              color: context.primaryColor,
              size: 25,
            ),
            const SizedBox(width: 10),
            Text(
              LocaleKeys.onboarding_manual_input.tr(),
              style:
                  context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w900, color: context.primaryColor),
            ),
          ],
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 35),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            EditIngredientCard(
              color: context.primaryColor,
              image: getIcon(widget.cardType),
              title: getTitle(),
              controller: _nutritionsController,
              focusNode: focusNode,
              onChanged: (val) {
                _nutritionsController.text = val;
                setState(() {});
              },
              value: 10.toString(),
            ),
            LargeButton(
              onPressed: () {
                widget.onSave(_nutritionsController.text);
                context.pop(_nutritionsController.text);
              },
              text: LocaleKeys.home_add.tr(),
              circularRadius: 16,
              backgroundColor: context.primaryColor,
              textStyle: context.textTheme.bodyMedium!.copyWith(
                color: context.onPrimaryColor,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
