// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database_food_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetDatabaseFoodModelCollection on Isar {
  IsarCollection<DatabaseFoodModel> get databaseFoodModels => this.collection();
}

const DatabaseFoodModelSchema = CollectionSchema(
  name: r'DatabaseFoodModel',
  id: 2666972659212452242,
  properties: {
    r'calories': PropertySchema(
      id: 0,
      name: r'calories',
      type: IsarType.long,
    ),
    r'carbs': PropertySchema(
      id: 1,
      name: r'carbs',
      type: IsarType.double,
    ),
    r'date': PropertySchema(
      id: 2,
      name: r'date',
      type: IsarType.dateTime,
    ),
    r'dish': PropertySchema(
      id: 3,
      name: r'dish',
      type: IsarType.string,
    ),
    r'fat': PropertySchema(
      id: 4,
      name: r'fat',
      type: IsarType.double,
    ),
    r'imagePath': PropertySchema(
      id: 5,
      name: r'imagePath',
      type: IsarType.string,
    ),
    r'ingredients': PropertySchema(
      id: 6,
      name: r'ingredients',
      type: IsarType.stringList,
    ),
    r'isDatabase': PropertySchema(
      id: 7,
      name: r'isDatabase',
      type: IsarType.bool,
    ),
    r'isFavoriteMeal': PropertySchema(
      id: 8,
      name: r'isFavoriteMeal',
      type: IsarType.bool,
    ),
    r'isFromSearch': PropertySchema(
      id: 9,
      name: r'isFromSearch',
      type: IsarType.bool,
    ),
    r'isHalal': PropertySchema(
      id: 10,
      name: r'isHalal',
      type: IsarType.bool,
    ),
    r'isMealCreated': PropertySchema(
      id: 11,
      name: r'isMealCreated',
      type: IsarType.bool,
    ),
    r'protein': PropertySchema(
      id: 12,
      name: r'protein',
      type: IsarType.double,
    )
  },
  estimateSize: _databaseFoodModelEstimateSize,
  serialize: _databaseFoodModelSerialize,
  deserialize: _databaseFoodModelDeserialize,
  deserializeProp: _databaseFoodModelDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _databaseFoodModelGetId,
  getLinks: _databaseFoodModelGetLinks,
  attach: _databaseFoodModelAttach,
  version: '3.1.0+1',
);

int _databaseFoodModelEstimateSize(
  DatabaseFoodModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.dish;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.imagePath;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.ingredients.length * 3;
  {
    for (var i = 0; i < object.ingredients.length; i++) {
      final value = object.ingredients[i];
      bytesCount += value.length * 3;
    }
  }
  return bytesCount;
}

void _databaseFoodModelSerialize(
  DatabaseFoodModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.calories);
  writer.writeDouble(offsets[1], object.carbs);
  writer.writeDateTime(offsets[2], object.date);
  writer.writeString(offsets[3], object.dish);
  writer.writeDouble(offsets[4], object.fat);
  writer.writeString(offsets[5], object.imagePath);
  writer.writeStringList(offsets[6], object.ingredients);
  writer.writeBool(offsets[7], object.isDatabase);
  writer.writeBool(offsets[8], object.isFavoriteMeal);
  writer.writeBool(offsets[9], object.isFromSearch);
  writer.writeBool(offsets[10], object.isHalal);
  writer.writeBool(offsets[11], object.isMealCreated);
  writer.writeDouble(offsets[12], object.protein);
}

DatabaseFoodModel _databaseFoodModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = DatabaseFoodModel(
    calories: reader.readLongOrNull(offsets[0]),
    carbs: reader.readDoubleOrNull(offsets[1]),
    date: reader.readDateTimeOrNull(offsets[2]),
    dish: reader.readStringOrNull(offsets[3]),
    fat: reader.readDoubleOrNull(offsets[4]),
    imagePath: reader.readStringOrNull(offsets[5]),
    ingredients: reader.readStringList(offsets[6]) ?? const [],
    isDatabase: reader.readBoolOrNull(offsets[7]) ?? false,
    isFavoriteMeal: reader.readBoolOrNull(offsets[8]) ?? false,
    isFromSearch: reader.readBoolOrNull(offsets[9]) ?? false,
    isHalal: reader.readBoolOrNull(offsets[10]),
    isMealCreated: reader.readBoolOrNull(offsets[11]) ?? false,
    protein: reader.readDoubleOrNull(offsets[12]),
  );
  object.id = id;
  return object;
}

P _databaseFoodModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readDoubleOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringList(offset) ?? const []) as P;
    case 7:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 8:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 9:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 10:
      return (reader.readBoolOrNull(offset)) as P;
    case 11:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 12:
      return (reader.readDoubleOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _databaseFoodModelGetId(DatabaseFoodModel object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _databaseFoodModelGetLinks(
    DatabaseFoodModel object) {
  return [];
}

void _databaseFoodModelAttach(
    IsarCollection<dynamic> col, Id id, DatabaseFoodModel object) {
  object.id = id;
}

extension DatabaseFoodModelQueryWhereSort
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QWhere> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension DatabaseFoodModelQueryWhere
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QWhereClause> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension DatabaseFoodModelQueryFilter
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QFilterCondition> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calories',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calories',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calories',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calories',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calories',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calories',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'carbs',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'carbs',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'carbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'carbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'carbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'carbs',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dish',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dish',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dish',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dish',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dish',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dish',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dish',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dish',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dish',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dish',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dish',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dishIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dish',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fat',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fat',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fat',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'imagePath',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'imagePath',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'imagePath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'imagePath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'imagePath',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'imagePath',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'ingredients',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'ingredients',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'ingredients',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'ingredients',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isDatabaseEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isDatabase',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isFavoriteMealEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isFavoriteMeal',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isFromSearchEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isFromSearch',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isHalalIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isHalal',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isHalalIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isHalal',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isHalalEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isHalal',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isMealCreatedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isMealCreated',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'protein',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'protein',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'protein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'protein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'protein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'protein',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }
}

extension DatabaseFoodModelQueryObject
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QFilterCondition> {}

extension DatabaseFoodModelQueryLinks
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QFilterCondition> {}

extension DatabaseFoodModelQuerySortBy
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QSortBy> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByDish() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dish', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByDishDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dish', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy> sortByFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByImagePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByImagePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsDatabase() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsDatabaseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFavoriteMeal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFavoriteMealDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFromSearch() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFromSearchDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsHalal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isHalal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsHalalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isHalal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsMealCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsMealCreatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.desc);
    });
  }
}

extension DatabaseFoodModelQuerySortThenBy
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QSortThenBy> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByDish() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dish', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByDishDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dish', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy> thenByFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByImagePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByImagePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsDatabase() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsDatabaseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFavoriteMeal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFavoriteMealDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFromSearch() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFromSearchDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsHalal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isHalal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsHalalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isHalal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsMealCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsMealCreatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.desc);
    });
  }
}

extension DatabaseFoodModelQueryWhereDistinct
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calories');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'carbs');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct> distinctByDish(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dish', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fat');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByImagePath({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'imagePath', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIngredients() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'ingredients');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsDatabase() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isDatabase');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsFavoriteMeal() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isFavoriteMeal');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsFromSearch() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isFromSearch');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsHalal() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isHalal');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsMealCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isMealCreated');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'protein');
    });
  }
}

extension DatabaseFoodModelQueryProperty
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QQueryProperty> {
  QueryBuilder<DatabaseFoodModel, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<DatabaseFoodModel, int?, QQueryOperations> caloriesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calories');
    });
  }

  QueryBuilder<DatabaseFoodModel, double?, QQueryOperations> carbsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'carbs');
    });
  }

  QueryBuilder<DatabaseFoodModel, DateTime?, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations> dishProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dish');
    });
  }

  QueryBuilder<DatabaseFoodModel, double?, QQueryOperations> fatProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fat');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations>
      imagePathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'imagePath');
    });
  }

  QueryBuilder<DatabaseFoodModel, List<String>, QQueryOperations>
      ingredientsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'ingredients');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations> isDatabaseProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isDatabase');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations>
      isFavoriteMealProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isFavoriteMeal');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations>
      isFromSearchProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isFromSearch');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool?, QQueryOperations> isHalalProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isHalal');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations>
      isMealCreatedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isMealCreated');
    });
  }

  QueryBuilder<DatabaseFoodModel, double?, QQueryOperations> proteinProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'protein');
    });
  }
}
