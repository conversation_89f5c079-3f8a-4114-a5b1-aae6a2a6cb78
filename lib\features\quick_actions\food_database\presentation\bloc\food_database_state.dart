part of 'food_database_bloc.dart';

enum FoodDatabaseStatus { initial, loading, success, failure }

class FoodDatabaseState extends Equatable {
  FoodDatabaseStatus status;
  List<DatabaseFoodModel> foodList;
  List<DatabaseFoodModel> recentFoodList;
  List<DatabaseFoodModel> myMealsList;
  List<DatabaseFoodModel> myFavoriteList;
  List<DatabaseFoodModel> databaseFoodList;
  List<DatabaseFoodModel> selectedFoods;
  DatabaseFoodModel? myMeal;
  String? errorMessage;

  List<RemoteFoodDatabaseModel>? searchedFood;
  BlocStatus? searchedFoodStatus;

  FoodDatabaseState({
    this.status = FoodDatabaseStatus.initial,
    this.foodList = const [],
    this.recentFoodList = const [],
    this.databaseFoodList = const [],
    this.myMealsList = const [],
    this.myFavoriteList = const [],
    this.selectedFoods = const [],
    this.errorMessage,
    this.searchedFood,
    this.searchedFoodStatus,
    this.myMeal,
  });

  FoodDatabaseState copyWith({
    FoodDatabaseStatus? status,
    List<DatabaseFoodModel>? foodList,
    List<DatabaseFoodModel>? recentFoodList,
    List<DatabaseFoodModel>? myMealsList,
    List<DatabaseFoodModel>? myFavoriteList,
    List<DatabaseFoodModel>? databaseFoodList,
    List<DatabaseFoodModel>? selectedFoods,
    DatabaseFoodModel? myMeal,
    String? errorMessage,
    List<RemoteFoodDatabaseModel>? searchedFood,
    BlocStatus? searchedFoodStatus,
  }) {
    return FoodDatabaseState(
      status: status ?? this.status,
      foodList: foodList ?? this.foodList,
      recentFoodList: recentFoodList ?? this.recentFoodList,
      selectedFoods: selectedFoods ?? this.selectedFoods,
      myMealsList: myMealsList ?? this.myMealsList,
      myFavoriteList: myFavoriteList ?? this.myFavoriteList,
      databaseFoodList: databaseFoodList ?? this.databaseFoodList,
      errorMessage: errorMessage ?? this.errorMessage,
      searchedFood: searchedFood ?? this.searchedFood,
      searchedFoodStatus: searchedFoodStatus ?? this.searchedFoodStatus,
      myMeal: myMeal ?? this.myMeal,
    );
  }

  @override
  List<Object?> get props => [
        status,
        foodList,
        recentFoodList,
        databaseFoodList,
        errorMessage,
        myFavoriteList,
        myMealsList,
        selectedFoods,
        searchedFood,
        searchedFoodStatus,
        myMeal,
      ];
}
