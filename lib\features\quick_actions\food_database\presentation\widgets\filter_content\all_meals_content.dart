import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/main/presentation/bloc/main_bloc.dart';
import 'package:cal/features/main/presentation/pages/main_screen.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/pages/create_meal_screen.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/enter_your_food_container.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../home/<USER>/bloc/recent_food_bloc.dart';

class AllMealsContent extends StatefulWidget {
  final bool showDatabaseList;
  final String searchQuery;

  const AllMealsContent({
    super.key,
    required this.showDatabaseList,
    required this.searchQuery,
  });

  @override
  State<AllMealsContent> createState() => _AllMealsContentState();
}

class _AllMealsContentState extends State<AllMealsContent> {
  @override
  void initState() {
    super.initState();
    context.read<FoodDatabaseBloc>().add(const LoadRecentFoodEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
      builder: (context, state) {
        return Column(
          children: [
            EnterYourFoodContainer(
              onPressed: () {
                context.push(BlocProvider(
                  create: (context) => getIt<FoodDatabaseBloc>(),
                  child: const CreateMealScreen(),
                ));
              },
              text: LocaleKeys.food_database_enter_your_meals.tr(),
              icon: const AppImage.asset(Assets.imagesEdit),
            ),
            const SizedBox(height: 19),
            Align(
              alignment: Alignment.centerRight,
              child: AppText.titleLarge(
                LocaleKeys.food_database_entrance_recently.tr(),
                color: context.onSecondary,
                fontWeight: FontWeight.w300,
              ),
            ),
            const SizedBox(height: 19),
            state.recentFoodList.isNotEmpty
                ? ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, i) => FoodDatabaseCard(
                      title: state.recentFoodList.reversed.toList()[i].dish ?? "unknown",
                      cals: state.recentFoodList.reversed.toList()[i].calories.toString(),
                      onAddTap: () {
                        getIt<RecentFoodBloc>().add(
                          AddFood(
                            isFromSearch: true,
                            meal: FoodModel(
                              fat: state.recentFoodList.reversed.toList()[i].fat,
                              carbs: state.recentFoodList.reversed.toList()[i].carbs,
                              protein: state.recentFoodList.reversed.toList()[i].protein,
                              calories: state.recentFoodList.reversed.toList()[i].calories,
                              dish: state.recentFoodList.reversed.toList()[i].dish,
                              isHalal: null,
                              date: DateTime.now(),
                              arabicName: (state.recentFoodList.reversed.toList()[i].dish),
                              englishName: (state.recentFoodList.reversed.toList()[i].dish),
                              ingredients: state.recentFoodList.reversed.toList()[i].ingredients,
                            ),
                          ),
                        );
                        context.read<FoodDatabaseBloc>().add(
                              AddFoodToLogEvent(
                                meal: FoodModel.fromDatabaseModel(
                                  state.recentFoodList.reversed.toList()[i].copyWith(date: DateTime.now()),
                                ),
                              ),
                            );
                        Future.delayed(const Duration(milliseconds: 200), () {
                          if (context.mounted) getIt<RecentFoodBloc>().add(LoadFood(date: DateTime.now()));
                          if (context.mounted) context.pop();
                        });
                      },
                    ),
                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                    itemCount: state.recentFoodList.length > 3 ? 3 : state.recentFoodList.length,
                  )
                : Align(
                    alignment: Alignment.center,
                    child: AppText.bodyMedium(
                      LocaleKeys.food_database_no_food.tr(),
                      color: context.onSecondary,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
            const SizedBox(height: 19),
            if (widget.showDatabaseList) ...[
              Align(
                alignment: Alignment.centerRight,
                child: AppText.titleLarge(
                  LocaleKeys.food_database_food_database.tr(),
                  color: context.onSecondary,
                  fontWeight: FontWeight.w300,
                ),
              ),
              const SizedBox(height: 19),
              BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                builder: (context, state) {
                  switch (state.searchedFoodStatus) {
                    case null:
                      return const SizedBox.shrink();
                    case BlocStatus.initial:
                      return const Center(
                        child: CircularProgressIndicator.adaptive(),
                      );
                    case BlocStatus.loading:
                      return const Center(
                        child: CircularProgressIndicator.adaptive(),
                      );
                    case BlocStatus.success:
                      return state.searchedFood!.isNotEmpty
                          ? ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, i) => FoodDatabaseCard(
                                title: state.searchedFood?[i].foodName ?? "unknown",
                                cals: state.searchedFood?[i].foodDescription.calories.toString(),
                                onAddTap: () {
                                  getIt<RecentFoodBloc>().add(
                                    AddFood(
                                      isFromSearch: true,
                                      meal: FoodModel(
                                        fat: state.searchedFood?[i].foodDescription.fat.toDouble(),
                                        calories: state.searchedFood?[i].foodDescription.calories.toInt(),
                                        carbs: state.searchedFood?[i].foodDescription.carbs.toDouble(),
                                        protein: state.searchedFood?[i].foodDescription.protein.toDouble(),
                                        arabicName: (state.searchedFood?[i].foodName),
                                        englishName: (state.searchedFood?[i].foodName),
                                        dish: state.searchedFood?[i].foodName,
                                        ingredients: state.searchedFood?[i].foodDescription.ingredients ?? [],
                                        isHalal: null,
                                        date: DateTime.now(),
                                      ),
                                    ),
                                  );
                                  context.read<FoodDatabaseBloc>().add(
                                        AddFoodToLogEvent(
                                          meal: FoodModel.fromDatabaseModel(
                                            DatabaseFoodModel(
                                              calories: state.searchedFood?[i].foodDescription.calories.toInt(),
                                              fat: state.searchedFood?[i].foodDescription.fat.toDouble(),
                                              carbs: state.searchedFood?[i].foodDescription.carbs.toDouble(),
                                              protein: state.searchedFood?[i].foodDescription.protein.toDouble(),
                                              dish: state.searchedFood?[i].foodName,
                                              ingredients: state.searchedFood?[i].foodDescription.ingredients ?? [],
                                              isFromSearch: true,
                                              isHalal: null,
                                              date: DateTime.now(),
                                            ),
                                          ),
                                        ),
                                      );
                                  context.read<FoodDatabaseBloc>().add(
                                        AddFoodEvent(
                                          meal: DatabaseFoodModel(
                                            calories: state.searchedFood?[i].foodDescription.calories.toInt(),
                                            fat: state.searchedFood?[i].foodDescription.fat.toDouble(),
                                            carbs: state.searchedFood?[i].foodDescription.carbs.toDouble(),
                                            protein: state.searchedFood?[i].foodDescription.protein.toDouble(),
                                            dish: state.searchedFood?[i].foodName,
                                            ingredients: state.searchedFood?[i].foodDescription.ingredients ?? [],
                                            isFromSearch: true,
                                            isHalal: null,
                                            date: DateTime.now(),
                                          ),
                                        ),
                                      );
                                  Future.delayed(const Duration(milliseconds: 200), () {
                                    if (context.mounted) getIt<RecentFoodBloc>().add(LoadFood(date: DateTime.now()));
                                    if (context.mounted) context.pop();
                                  });
                                },
                              ),
                              separatorBuilder: (context, index) => const SizedBox(height: 16),
                              itemCount: state.searchedFood!.length,
                            )
                          : Align(
                              alignment: Alignment.center,
                              child: AppText.bodyMedium(
                                LocaleKeys.food_database_no_food.tr(),
                                color: context.onSecondary,
                                fontWeight: FontWeight.w300,
                              ),
                            );
                    case BlocStatus.error:
                      return Align(
                        alignment: Alignment.center,
                        child: AppText.bodyMedium(
                          LocaleKeys.food_database_no_food.tr(),
                          color: context.onSecondary,
                          fontWeight: FontWeight.w300,
                        ),
                      );
                  }
                },
              ),
            ]
          ],
        );
      },
    );
  }
}
/*state.databaseFoodList.isNotEmpty
                  ? ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, i) => FoodDatabaseCard(
                        title: filteredDatabaseList[i].dish ?? "unknown",
                        cals: filteredDatabaseList[i].calories.toString(),
                        onAddTap: () {
                          // context.read<FoodDatabaseBloc>().add(event);
                        },
                      ),
                      separatorBuilder: (context, index) => const SizedBox(height: 16),
                      itemCount: filteredDatabaseList.length,
                    )
                  : Align(
                      alignment: Alignment.center,
                      child: AppText.bodyMedium(
                        LocaleKeys.food_database_no_food.tr(),
                        color: context.onSecondary,
                        fontWeight: FontWeight.w300,
                      ),
                    ),*/
