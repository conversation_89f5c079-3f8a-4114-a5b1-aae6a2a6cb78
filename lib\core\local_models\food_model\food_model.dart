import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'food_model.g.dart';

@Collection(inheritance: false)
// ignore: must_be_immutable
class FoodModel extends Equatable {
  Id id = Isar.autoIncrement;

  late String? dish;
  late String? arabicName;
  late String? englishName;
  late int? calories;
  late double? protein;
  late double? carbs;
  late double? fat;
  late List<String> ingredients;
  late bool? isHalal;

  late String? imagePath;
  late DateTime? date;

  @ignore
  bool isLoading = false;
  @ignore
  bool hasError;
  @ignore
  String? tempId;

  FoodModel({
    this.dish,
    this.calories,
    this.protein,
    this.carbs,
    this.fat,
    this.isHalal,
    DateTime? date,
    this.imagePath,
    this.isLoading = false,
    this.englishName,
    this.arabicName,
    this.hasError = false,
    this.tempId,
    this.ingredients = const [],
  }) : date = date ?? DateTime.now();

  factory FoodModel.fromDatabaseModel(DatabaseFoodModel db) {
    return FoodModel(
      dish: db.dish ?? 'unknown',
      calories: db.calories ?? 0,
      protein: db.protein ?? 0,
      carbs: db.carbs ?? 0,
      fat: db.fat ?? 0,
      isHalal: db.isHalal,
      imagePath: db.imagePath,
      ingredients: db.ingredients,
      date: DateTime.now(),
    );
  }

  FoodModel copyWith({
    Id? id,
    String? dish,
    int? calories,
    double? protein,
    double? carbs,
    double? fat,
    bool? isHalal,
    DateTime? date,
    String? imagePath,
    bool? isLoading,
    bool? hasError,
    String? englishName,
    String? arabicName,
    String? tempId,
    List<String>? ingredients,
  }) {
    final copy = FoodModel(
      dish: dish ?? this.dish,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      fat: fat ?? this.fat,
      isHalal: isHalal ?? this.isHalal,
      date: date ?? this.date,
      imagePath: imagePath ?? this.imagePath,
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      englishName: englishName ?? this.englishName,
      arabicName: arabicName ?? this.arabicName,
      tempId: tempId ?? this.tempId,
      // ingredients: ingredients == ?? this.ingredients,
    );

    copy.id = id ?? this.id;
    return copy;
  }

  factory FoodModel.fromJson(Map<String, dynamic> fullJson) {
    final json = fullJson['data'];
    return FoodModel(
      dish: json['dish'] as String?,
      calories: json['calories'] as int,
      protein: (json['protein'] as num).toDouble(),
      carbs: (json['carbs'] as num).toDouble(),
      fat: (json['fat'] as num).toDouble(),
      isHalal: json['halal'] as bool?,
      englishName: json['english_name'] as String?,
      arabicName: json['arabic_name'] as String?,
      // ingredients: json['ingredients'] as List<String>? ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dish': dish,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'isHalal': isHalal,
      'date': date?.toIso8601String(),
      'imagePath': imagePath,
      'isLoading': isLoading,
      'english_name': englishName,
      'arabic_name': arabicName,
      'ingredients': ingredients
    };
  }

  @override
  @ignore
  List<Object?> get props => [
        dish,
        calories,
        protein,
        carbs,
        fat,
        isHalal,
        date,
        imagePath,
        hasError,
        isLoading,
        tempId,
        ingredients,
      ];
}
