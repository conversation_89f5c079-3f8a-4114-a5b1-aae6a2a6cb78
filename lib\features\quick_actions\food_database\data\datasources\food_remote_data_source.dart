import 'package:cal/core/config/endpoints.dart';
import 'package:cal/features/quick_actions/food_database/data/models/remote_food_database_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:injectable/injectable.dart';

import '../../../../../core/network/api_handler.dart';
import '../../../../../core/network/http_client.dart';

@lazySingleton
class FoodRemoteDataSource with ApiHandler {
  
  final HTTPClient dioClient;
  
  FoodRemoteDataSource({required this.dioClient});
  
  Future<List<RemoteFoodDatabaseModel>> searchMeals(SearchMealsParams params) async {
    return wrapHandlingApi(tryCall: () => dioClient.get(FoodDatabaseEndPoint.search(params.query)), jsonConvert: remoteFoodDatabaseModelFromJson);
  }
  
}