import 'dart:async';
import 'package:cal/features/home/<USER>/bloc/recent_food_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:cal/features/home/<USER>/pages/item_details_screen.dart';
import 'food_card_image.dart';
import 'food_card_details.dart';

class FoodCard extends StatefulWidget {
  const FoodCard({
    super.key,
    required this.foodModel,
    required this.onDelete,
    this.onRetry,
  });

  final FoodModel foodModel;
  final VoidCallback onDelete;
  final VoidCallback? onRetry;

  @override
  State<FoodCard> createState() => _FoodCardState();
}

class _FoodCardState extends State<FoodCard> with SingleTickerProviderStateMixin {
  double _progress = 0.0;
  Timer? _successTimer;
  late AnimationController _controller;
  late Animation<double> _animatedProgress;
  bool _isCompleting = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();

    if (widget.foodModel.isLoading) {
      _controller.forward();
    }
  }

  void _initializeAnimation() {
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    );

    _animatedProgress = Tween<double>(begin: 0.0, end: 0.9).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOutCubic,
      ),
    )..addListener(() {
        if (mounted && !_isCompleting) {
          setState(() {
            _progress = _animatedProgress.value;
          });
        }
      });
  }

  @override
  void didUpdateWidget(covariant FoodCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!oldWidget.foodModel.isLoading && widget.foodModel.isLoading) {
      _progress = 0.0;
      _controller.reset();
      _controller.forward();
    }

    if (oldWidget.foodModel.isLoading && !widget.foodModel.isLoading) {
      _controller.stop();
      _animateToCompletion();
    }
  }

  void _animateToCompletion() {
    _isCompleting = true;
    const targetProgress = 1.0;
    const steps = 25;
    const stepDuration = Duration(milliseconds: 30);

    final startProgress = _progress;
    final progressDifference = targetProgress - startProgress;
    final progressPerStep = progressDifference / steps;

    int currentStep = 0;

    _successTimer = Timer.periodic(stepDuration, (timer) {
      if (currentStep < steps) {
        setState(() {
          _progress = startProgress + (progressPerStep * (currentStep + 1));
        });
        currentStep++;
      } else {
        setState(() {
          _progress = targetProgress;
        });
        timer.cancel();

        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            setState(() {
              _isCompleting = false;
              _progress = 0.0;
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _successTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  bool get _shouldShowProgress {
    return widget.foodModel.isLoading || (_isCompleting && _progress < 1.0);
  }

  // Push with transition
  void _navigateToItemDetails(BuildContext ctx) {
    if (widget.foodModel.isLoading || widget.foodModel.hasError) return;

    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => BlocProvider.value(
          value: ctx.read<RecentFoodBloc>(),
          child: ItemDetailsScreen(
            foodModel: widget.foodModel,
          ),
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          var offsetAnimation = animation.drive(tween);

          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey("${widget.foodModel.imagePath}_${widget.foodModel.isLoading}_${widget.foodModel.hasError}"),
      endActionPane: _buildActionPane(),
      child: GestureDetector(
        onTap: () => _navigateToItemDetails(context),
        child: _buildCardContainer(context),
      ),
    );
  }

  ActionPane _buildActionPane() {
    return ActionPane(
      motion: const DrawerMotion(),
      extentRatio: 0.25,
      children: [
        SlidableAction(
          onPressed: (_) => widget.onDelete(),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          icon: Icons.delete,
          label: LocaleKeys.home_delete.tr(),
          borderRadius: BorderRadius.circular(20),
        ),
      ],
    );
  }

  Widget _buildCardContainer(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: const Color(0xfffefefe),
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(35),
            offset: const Offset(-2, 3),
            blurRadius: 10,
          ),
        ],
      ),
      child: Row(
        children: [
          FoodCardImage(
            foodModel: widget.foodModel,
            progress: _progress,
            isLoading: _shouldShowProgress,
            onRetry: widget.onRetry,
          ),
          const SizedBox(width: 0),
          FoodCardDetails(
            foodModel: widget.foodModel,
            isLoading: _shouldShowProgress,
          ),
        ],
      ),
    );
  }
}
