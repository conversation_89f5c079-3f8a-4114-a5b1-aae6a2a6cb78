import 'package:equatable/equatable.dart';
import 'package:isar/isar.dart';

part 'database_food_model.g.dart';

@Collection(inheritance: false)
// ignore: must_be_immutable
class DatabaseFoodModel extends Equatable {
  Id id = Isar.autoIncrement;

  late String? dish;
  late int? calories;
  late double? protein;
  late double? carbs;
  late double? fat;
  late bool? isHalal;
  late List<String> ingredients;
  late bool isFromSearch;
  late bool isDatabase;
  late bool isMealCreated;
  late bool isFavoriteMeal;

  late String? imagePath;
  late DateTime? date;

  @ignore
  bool isLoading = false;
  @ignore
  bool hasError;

  DatabaseFoodModel({
    this.dish,
    this.calories,
    this.protein,
    this.carbs,
    this.fat,
    this.isHalal,
    this.isFromSearch = false,
    this.isDatabase = false,
    this.isMealCreated = false,
    this.isFavoriteMeal = false,
    DateTime? date,
    this.imagePath,
    this.isLoading = false,
    this.hasError = false,
    this.ingredients = const [],
  }) : date = date ?? DateTime.now();

  DatabaseFoodModel copyWith({
    String? dish,
    int? calories,
    double? protein,
    double? carbs,
    double? fat,
    bool? isHalal,
    bool? isFromSearch,
    bool? isDatabase,
    bool? isMealCreated,
    bool? isFavoriteFood,
    bool? isFavoriteMeal,
    DateTime? date,
    String? imagePath,
    bool? isLoading,
    bool? hasError,
    List<String>? ingredients,
  }) {
    return DatabaseFoodModel(
      dish: dish ?? this.dish,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      carbs: carbs ?? this.carbs,
      fat: fat ?? this.fat,
      isHalal: isHalal,
      isFromSearch: isFromSearch ?? this.isFromSearch,
      isFavoriteMeal: isFavoriteMeal ?? this.isFavoriteMeal,
      isDatabase: isDatabase ?? this.isDatabase,
      isMealCreated: isMealCreated ?? this.isMealCreated,
      date: date ?? this.date,
      imagePath: imagePath ?? this.imagePath,
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      ingredients: ingredients ?? this.ingredients,
    );
  }

  factory DatabaseFoodModel.fromJson(Map<String, dynamic> fullJson) {
    final json = fullJson['data'];
    return DatabaseFoodModel(
      dish: json['dish'] as String?,
      calories: json['calories'] as int,
      protein: (json['protein'] as num).toDouble(),
      carbs: (json['carbs'] as num).toDouble(),
      fat: (json['fat'] as num).toDouble(),
      isHalal: json['halal'] as bool,
      ingredients: json['ingredients'] as List<String>,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dish': dish,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'isHalal': isHalal,
      'isDatabase': isDatabase,
      'date': date?.toIso8601String(),
      'imagePath': imagePath,
      'isLoading': isLoading,
      'ingredients': ingredients,
    };
  }

  @override
  @ignore
  List<Object?> get props => [
        dish,
        calories,
        protein,
        carbs,
        fat,
        isHalal,
        isDatabase,
        isFavoriteMeal,
        isMealCreated,
        date,
        imagePath,
        hasError,
        isLoading,
        ingredients,
      ];
}
