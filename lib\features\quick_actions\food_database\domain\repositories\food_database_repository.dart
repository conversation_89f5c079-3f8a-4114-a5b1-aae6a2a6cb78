import 'package:cal/common/consts/typedef.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/data/models/remote_food_database_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';

abstract class FoodDatabaseRepository {
  Future<void> saveMeal(DatabaseFoodModel food);
  Future<void> saveMealToLog(FoodModel food);
  Future<List<DatabaseFoodModel>> getRecentFood();
  Future<List<DatabaseFoodModel>> getMyMeals();
  Future<List<DatabaseFoodModel>> getDatabaseFood();
  Future<List<DatabaseFoodModel>> getFavoriteFood();
  DataResponse<List<RemoteFoodDatabaseModel>> searchMeals(SearchMealsParams params);
}
